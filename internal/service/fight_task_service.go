package service

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"
	device_enum "wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	device_request "wukong-api/cloud_sdk/cloud_api/device/request"
	"wukong-api/cloud_sdk/cloud_api/wayline/cloud_enum"
	wayline_cloud_enum "wukong-api/cloud_sdk/cloud_api/wayline/cloud_enum"
	"wukong-api/cloud_sdk/cloud_api/wayline/request"
	"wukong-api/cloud_sdk/cloud_api/wayline/response"
	"wukong-api/cloud_sdk/mqtt"
	"wukong-api/internal/cloudinfra"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/internal/fileds"
	"wukong-api/internal/model"
	"wukong-api/internal/repo"
	"wukong-api/pkg/utils"

	"github.com/google/uuid"

	"github.com/go-redis/redis"
	"github.com/qxsugar/pkg/dbx"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	fightTaskServiceOnce sync.Once
	fightTaskService     *FightTaskService
)

type FightTaskService struct {
	db                         *gorm.DB
	redis                      *redis.Client
	logger                     *zap.SugaredLogger
	waylineFileService         *WaylineFileService
	waylineJobService          *WaylineJobService
	waylineRedisService        *WaylineRedisService
	mediaRedisService          *MediaRedisService
	deviceRedisService         *DeviceRedisService
	deviceService              *DeviceService
	inspectionJobRecordService *InspectionJobRecordService
	liveStreamService          *LiveStreamService
	jobLogsSvc                 *JobLogsService
}

func NewFightTaskService() *FightTaskService {
	fightTaskServiceOnce.Do(func() {
		fightTaskService = &FightTaskService{
			waylineJobService:          NewWaylineJobService(),
			waylineFileService:         NewWaylineFileService(),
			waylineRedisService:        NewWaylineRedisService(),
			mediaRedisService:          NewMediaRedisService(),
			deviceRedisService:         NewDeviceRedisService(),
			deviceService:              NewDeviceService(),
			inspectionJobRecordService: NewInspectionJobRecordService(),
			liveStreamService:          NewLiveStreamService(),
			jobLogsSvc:                 NewJobLogsService(),
			db:                         repo.GetDatabase(),
			redis:                      repo.GetRedis(0),
			logger:                     repo.GetLogger(),
		}
	})
	return fightTaskService
}

const (
	waylineFileSignExpire = 60 * 60 * 24
)

// createFlightTask 创建航线任务
func (s *FightTaskService) createFlightTask(inspectionJob model.InspectionJob) (*dto.CreateJobResp, error, string) {
	var taskType cloud_enum.TaskTypeEnum
	switch inspectionJob.ExecuteType {
	case 1:
		taskType = cloud_enum.IMMEDIATE
	case 2:
		taskType = cloud_enum.TIMED
	case 3:
		taskType = cloud_enum.TIMED
	}

	if cloud_enum.IMMEDIATE == taskType {
		return s.publishFlightTask(inspectionJob, taskType, false, nil, inspectionJob.CreatorID)
	} else if cloud_enum.CONDITIONAL == taskType {
		// todo 如果条件任务结束时间小于当前时间，则退出
		//now := time.Now().Unix()
		//if enum.IMMEDIATE != param.TaskType && param.EndTime < now {
		//	return nil, nil
		//}

		// todo 给条件型任务设置可执行条件和就绪条件；将条件型任务存入redis
	}

	return nil, nil, ""
}

// publishFlightTask 发布航线任务
// isTmp 是否一键起飞
// url 一键起飞时的kml文件
func (s *FightTaskService) publishFlightTask(inspectionJob model.InspectionJob, taskType cloud_enum.TaskTypeEnum, isTmp bool, wf *model.WaylineFile, creatorID int) (*dto.CreateJobResp, error, string) {
	dockSn := inspectionJob.DockSN
	minBatteryCapacity := inspectionJob.MinBatteryCapacity

	device, err := s.deviceService.GetDeviceBySn(dockSn)
	if err != nil {
		return nil, err, ""
	}

	isDock2 := device.DeviceType == device_enum.Device_DOCK2.Value()

	var waylineParsedData *dto.WaylineParsedDataDto
	if isTmp {
		url, err := NewOssService().GetObjectSignURL(wf.ObjectKey, 60*60)
		if err != nil {
			return nil, err, ""
		}
		kmlData, err := NewWaylineFileService().ParseKML(url)
		if err != nil {
			return nil, err, ""
		}

		targetTrajectoryData, err := json.Marshal(kmlData.Points)
		if err != nil {
			return nil, err, ""
		}

		waylineParsedData = &dto.WaylineParsedDataDto{
			WaylineFileId:        wf.ID,
			TargetTrajectoryData: string(targetTrajectoryData),
			ReturnKMLData:        *kmlData,
		}
	} else {
		// fixme 目前只关注一个航线
		var waylineManageIDList []int
		if err = json.Unmarshal([]byte(inspectionJob.WaylineManageIDList), &waylineManageIDList); err != nil {
			return nil, err, ""
		}
		if len(waylineManageIDList) == 0 {
			return nil, fmt.Errorf("请选择航线"), "请选择航线"
		}
		wd, err := NewWaylineManageService().GetParsedDataById(waylineManageIDList[0])
		if err != nil {
			return nil, err, ""
		}
		waylineParsedData = wd
	}

	createJobReq := &dto.CreateJobReq{
		FileId:             waylineParsedData.WaylineFileId,
		DockSn:             inspectionJob.DockSN,
		TaskType:           taskType,
		RthAltitude:        inspectionJob.RTHAltitude,
		OutOfControlAction: inspectionJob.OutOfControl,
		MinBatteryCapacity: minBatteryCapacity,
		//MinStorageCapacity: inspectionJob.MinStorageCapacity,
		SimulateMission: request.SimulateMission{
			IsEnable:  cloud_enum.FindSimulateSwitchEnum(inspectionJob.IsMock),
			Latitude:  inspectionJob.MockLatitude,
			Longitude: inspectionJob.MockLongitude,
		},
	}

	waylineJob, err := s.waylineJobService.CreateWaylineJob(createJobReq, inspectionJob.CompanyID, createJobReq.SimulateMission.IsEnable.Value())
	if err != nil {
		return nil, err, ""
	}

	waylineJobId := waylineJob.JobID

	var jobRecord *model.InspectionJobRecord
	jobRecord, err = s.inspectionJobRecordService.Create(inspectionJob, creatorID, "", waylineJobId, *waylineParsedData, isTmp)
	if err != nil {
		return nil, err, ""
	}

	jobRecordId := jobRecord.ID
	droneSn := waylineJob.DroneSN

	online, err := s.deviceRedisService.CheckDeviceOnline(dockSn)
	if err != nil {
		return nil, err, ""
	} else if !online {
		_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJobId, dockSn, "航线任务下发失败，机场处于离线中", true)
		_ = s.waylineJobService.UpdateWaylineJobStatus(enum.FAILED.Value(), waylineJobId, nil)
		return nil, fmt.Errorf("dock %s is offline", dockSn), "机场处于离线中"
	}

	if device.JobId != "" {
		_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJobId, dockSn, "航线任务下发失败，该机场正在其他执行任务", true)
		_ = s.waylineJobService.UpdateWaylineJobStatus(enum.FAILED.Value(), waylineJobId, nil)
		return nil, fmt.Errorf("%s该机场正在执行任务", dockSn), "该机场正在其他执行任务"
	}

	batteryFlag, curBattery, _ := s.deviceService.checkDroneBatteryIsEnoughByDock(dockSn, minBatteryCapacity)
	if !batteryFlag {
		_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJobId, dockSn, fmt.Sprintf("航线任务下发失败，无人机电量小于最低电量要求，当前电量: %d，最低电量要求: %v", curBattery, minBatteryCapacity), true)
		_ = s.waylineJobService.UpdateWaylineJobStatus(enum.FAILED.Value(), waylineJobId, nil)
		return nil, fmt.Errorf("无人机电量小于最低电量要求:%v, 当前电量:%v", minBatteryCapacity, curBattery), "无人机电量小于最低电量要求"
	}

	droneOnline, _ := s.deviceRedisService.CheckDeviceOnline(droneSn)

	// 清除无人机直播状态
	liveStreamingKey := fmt.Sprintf(fileds.LIVE_STREAMING, droneSn)
	result, _ := s.redis.Get(liveStreamingKey).Result()

	if result != "" {
		if droneOnline {
			// 如果无人机在线且直播状态未关闭，关闭直播并清除直播状态
			err = s.liveStreamService.StopLiveStreamWithDrone(waylineJob.DroneSN)
			if err != nil {
				s.logger.Errorf("publishFlightTask: stop live stream failed, dockSn: %s, err: %v", dockSn, err)
				return nil, err, ""
			}
			s.logger.Debugf("publishFlightTask: stop live stream success, droneSn: %s", droneSn)
		} else {
			// 如果无人机离线且直播状态未关闭，清除直播状态
			err = s.deviceRedisService.DelDeviceLiveStreaming(droneSn)
			if err != nil {
				s.logger.Errorf("publishFlightTask: del drone live stream failed, droneSn: %s, err: %v", droneSn, err)
				return nil, err, ""
			}
			s.logger.Debugf("publishFlightTask: del live streaming, droneSn: %s", droneSn)
		}
	}

	// 下发任务
	resultCode, err := s.prepareFlightTask(*waylineJob, createJobReq.SimulateMission, isDock2)
	if err != nil {
		var desc string
		if resultCode != nil {
			errorCodeEnum := cloud_enum.FindWaylineErrorCodeEnum(*resultCode)
			desc = fmt.Sprintf("航线任务下发失败，预下发阶段异常: %s", errorCodeEnum.String())
		} else {
			desc = "航线任务下发失败，预下发阶段异常"
		}

		_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJobId, dockSn, desc, true)
		_ = s.waylineJobService.UpdateWaylineJobStatus(enum.FAILED.Value(), waylineJobId, resultCode)
		return nil, err, desc
	}

	if err = s.waylineJobService.UpdateWaylineJobStatus(enum.Prepared.Value(), waylineJobId, resultCode); err != nil {
		return nil, err, ""
	}

	// 执行任务
	var execResultCode *int
	execResultCode, err = s.executeFlightTask(waylineJobId, isDock2)
	if err != nil {
		var desc string
		if execResultCode != nil {
			errorCodeEnum := cloud_enum.FindWaylineErrorCodeEnum(*execResultCode)
			desc = fmt.Sprintf("航线任务下发失败，启动执行阶段异常: %s", errorCodeEnum.String())
		} else {
			desc = "航线任务下发失败，启动执行阶段异常"
		}

		_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJobId, dockSn, desc, true)
		_ = s.waylineJobService.UpdateWaylineJobStatus(enum.FAILED.Value(), waylineJobId, execResultCode)
		return nil, err, desc
	}

	// 清缓存，避免脏数据
	NewDroneMsgService().ClearDroneTaskDataCache(waylineJobId, droneSn)

	if err = s.waylineJobService.UpdateWaylineJobStatus(enum.IN_PROGRESS.Value(), waylineJobId, execResultCode); err != nil {
		return nil, err, ""
	}

	err = NewDroneService().UpdateJobIdByDockSn(enum.JobTypeEnum_Wayline.Value(), waylineJobId, dockSn)
	if err != nil {
		return nil, err, ""
	}

	columns := map[string]interface{}{
		"execute_time": dbx.TimeStamp{Time: time.Now()},
		"percent":      1,
	}

	err = s.db.Model(&model.InspectionJobRecord{}).Where("id = ?", jobRecordId).Updates(columns).Error
	if err != nil {
		return nil, err, ""
	}

	err = s.deviceService.updateDeviceJob(dockSn, waylineJobId)
	if err != nil {
		return nil, err, ""
	}

	go func() {
		_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJobId, dockSn, "航线任务开始下发", false)
		_ = s.initFlightTaskTrajectory(dockSn, droneSn, jobRecordId)
	}()

	return &dto.CreateJobResp{
		JobRecordId:  jobRecordId,
		WaylineJobId: waylineJobId,
		Wayline:      waylineParsedData.ReturnKMLData.Points,
	}, nil, ""
}

// prepareFlightTask 给机场下发任务
func (s *FightTaskService) prepareFlightTask(waylineJob model.WaylineJob, simulateMission request.SimulateMission, isDock2 bool) (*int, error) {
	dockSn := waylineJob.DockSN
	waylineFile, err := NewWaylineFileService().GetWaylineById(waylineJob.FileID)
	if err != nil {
		return nil, err
	}

	url, err := NewOssService().GetObjectSignURL(waylineFile.ObjectKey, waylineFileSignExpire)
	if err != nil {
		return nil, err
	}

	fingerprint, err := utils.GetRemoteFileMD5(url)
	if err != nil {
		return nil, err
	}

	flightTask := request.FlightTaskPrepareRequest{
		FlightId:              waylineJob.JobID,
		ExecuteTime:           int(time.Now().UnixMilli()),
		TaskType:              cloud_enum.FindTaskTypeEnum(waylineJob.TaskType),
		WaylineType:           waylineJob.WaylineType,
		File:                  request.FlightTaskFile{Url: url, Fingerprint: fingerprint},
		RthAltitude:           waylineJob.RthAltitude,
		OutOfControlAction:    cloud_enum.FindOutOfControlActionEnum(waylineJob.OutOfControl),
		ExitWaylineWhenRcLost: cloud_enum.FindExitWaylineWhenRcLostEnum(cloud_enum.EXECUTE_RC_LOST_ACTION.Value()),
		//ReadyConditions:          request.ReadyConditions{},
		//ExecutableConditions:     request.ExecutableConditions{},
		//BreakPoint:               request.FlightTaskBreakPoint{},
		RthMode: cloud_enum.FindRthModeEnum(cloud_enum.PRESET_HEIGHT.Value()),
		//SimulateMission: simulateMission,
	}

	if isDock2 {
		flightTask.RthMode = cloud_enum.FindRthModeEnum(cloud_enum.PRESET_HEIGHT.Value())
		flightTask.WaylinePrecisionType = cloud_enum.FindWaylinePrecisionTypeEnum(cloud_enum.RTK.Value())
	}

	// todo 条件类型：添加条件

	serviceReply, err := cloudinfra.FlightTaskPrepare(dockSn, flightTask)
	if err != nil {
		s.logger.Errorf("prepare flight task failed, jobId: %s, err: %v", waylineJob.JobID, err)
		return nil, err
	}

	if *serviceReply.Data.Result != 0 {
		return serviceReply.Data.Result, fmt.Errorf("prepare flight task failed, jobId: %s, err: %d", waylineJob.JobID, *serviceReply.Data.Result)
	}

	return nil, nil
}

// executeFlightTask 立即执行任务
func (s *FightTaskService) executeFlightTask(jobId string, isDock2 bool) (*int, error) {
	waylineJob, err := s.waylineJobService.GetWaylineJobByJobId(jobId)
	if err != nil {
		return nil, err
	}

	online, err := s.deviceRedisService.CheckDeviceOnline(waylineJob.DockSN)
	if err != nil {
		return nil, err
	} else if !online {
		return nil, fmt.Errorf("dock %s is offline", waylineJob.DockSN)
	}

	flightTask := request.FlightTaskExecuteRequest{
		FlightId: waylineJob.JobID,
	}

	serviceReply, err := cloudinfra.FlightTaskExecute(waylineJob.DockSN, flightTask)
	if err != nil {
		s.logger.Errorf("execute flight task failed, jobId: %s, err: %v", waylineJob.JobID, err)
		return nil, err
	}

	data := mqtt.EventsReceiver[request.FlightTaskProgress]{
		//Result: 0,
		//Output: wayline.FlightTaskProgress{
		//	Ext:      wayline.FlightTaskProgressExt{},
		//	Progress: wayline.FlightTaskProgressData{},
		//	Status:   "",
		//},
		Bid: waylineJob.JobID,
		Sn:  waylineJob.DockSN,
	}

	if *serviceReply.Data.Result != 0 {
		return serviceReply.Data.Result, fmt.Errorf("execute flight task failed, jobId: %s, err: %d", waylineJob.JobID, serviceReply.Data.Result)
	}

	err = s.waylineRedisService.SetRunningWaylineJob(waylineJob.DockSN, data)
	if err != nil {
		s.logger.Errorf("execute flight task set RunningWaylineJob failed, jobId: %s, err: %v", waylineJob.JobID, err)
		return nil, err
	}

	return nil, nil
}

// flightTaskResourceGetHandle 航线任务资源获取处理
func (s *FightTaskService) flightTaskResourceGetHandle(req mqtt.TopicRequestsRequest[request.FlightTaskResourceGetRequest]) error {
	jobId := req.Data.FlightId

	waylineJob, err := s.waylineJobService.GetWaylineJobByJobId(jobId)
	if err != nil {
		return err
	}

	// 检查dock在线状态
	online, err := s.deviceRedisService.CheckDeviceOnline(waylineJob.DockSN)
	if err != nil {
		return err
	} else if !online {
		err = fmt.Errorf("dock %s is offline", waylineJob.DockSN)
		return err
	}

	waylineFile, err := NewWaylineFileService().GetWaylineById(waylineJob.FileID)
	if err != nil {
		return err
	}

	url, err := NewOssService().GetObjectSignURL(waylineFile.ObjectKey, waylineFileSignExpire)
	if err != nil {
		return err
	}

	fingerprint, err := utils.GetRemoteFileMD5(url)
	if err != nil {
		return err
	}

	data := response.FlightTaskResourceGetResponse{
		File: request.FlightTaskFile{
			Url:         url,
			Fingerprint: fingerprint,
		},
	}

	err = cloudinfra.RequestsReply(fileds.FLIGHTTASK_RESOURCE_GET, waylineJob.DockSN, req.Bid, req.Tid, data)
	if err != nil {
		s.logger.Errorf("flightTaskResourceGetHandle, RequestsReply jobId: %s, err: %v", waylineJob.JobID, err)
		return err
	}

	return nil
}

// flightTaskProgressHandle 航线任务进度上报处理
func (s *FightTaskService) flightTaskProgressHandle(req mqtt.TopicEventsRequest[mqtt.EventsDataRequest[request.FlightTaskProgress]]) error {
	jobId := req.Bid
	gatewaySn := req.Gateway

	// 添加分布式锁防止同一任务的并发处理，解决MySQL锁等待超时问题
	lockKey := fmt.Sprintf("flight_task_progress:%s:%s", gatewaySn, jobId)
	acquired, err := s.redis.SetNX(lockKey, "locked", 30*time.Second).Result()
	if err != nil {
		s.logger.Errorf("flightTaskProgressHandle: failed to acquire lock, jobId: %s, err: %v", jobId, err)
		return fmt.Errorf("failed to acquire lock: %v", err)
	}
	if !acquired {
		s.logger.Warnf("flightTaskProgressHandle: another progress update is in progress, skipping: tid=%s, jobId=%s, gateway=%s", req.Tid, jobId, gatewaySn)
		return nil // 跳过而不是报错，避免重复处理
	}
	defer func() {
		if delErr := s.redis.Del(lockKey).Err(); delErr != nil {
			s.logger.Errorf("flightTaskProgressHandle: failed to release lock, jobId: %s, err: %v", jobId, delErr)
		}
	}()

	waylineJob, err := s.waylineJobService.GetWaylineJobByJobId(jobId)
	if err != nil {
		return err
	}

	var gateway *dto.DeviceDTO
	gateway, err = s.deviceRedisService.getDeviceOnline(gatewaySn)
	if err != nil {
		return err
	}

	var jobRecord *model.InspectionJobRecord
	jobRecord, err = s.inspectionJobRecordService.getByWaylineJobId(waylineJob.JobID)
	if err != nil {
		return err
	}

	isNotDone := jobRecord.CompletedTime.IsZero()
	if !isNotDone {
		// 任务已经结束，不再重复处理
		s.logger.Info("flightTaskProgressHandle, jobRecordId: %s, 任务已经结束，不再重复处理, bid: %s, mediaCount: %d", jobRecord.ID, req.Bid, req.Data.Output.Ext.MediaCount)
		if req.NeedReply == 1 {
			err = cloudinfra.EventsReply(fileds.FLIGHTTASK_PROGRESS, gatewaySn, req.Bid, req.Tid)
			if err != nil {
				s.logger.Errorf("FlightTaskProgressHandle, FlightTaskProgressReply failed, jobId: %s, err: %v", jobId, err)
				return err
			}
		}
	}

	eventsReceiver := mqtt.EventsReceiver[request.FlightTaskProgress]{
		Result: req.Data.Result,
		Output: req.Data.Output,
		Bid:    req.Bid,
		Sn:     req.Gateway,
	}

	err = s.waylineRedisService.SetRunningWaylineJob(gatewaySn, eventsReceiver)
	if err != nil {
		s.logger.Errorf("flight task progress set running wayline job failed, jobId: %s, err: %v", jobId, err)
		return err
	}

	// 降落
	if gateway != nil && jobRecord.Percent < 99 && eventsReceiver.Output.Progress.CurrentStep != nil {
		var updateFlag bool

		if gateway.DeviceType == device_enum.Device_DOCK.Value() {
			// 机场1，飞行器降落机场
			if eventsReceiver.Output.Progress.CurrentStep.Value() == 25 {
				updateFlag = true
			}
		} else if gateway.DeviceType == device_enum.Device_DOCK2.Value() {
			// 机场2，飞行器降落机场
			if eventsReceiver.Output.Progress.CurrentStep.Value() == 27 {
				updateFlag = true
			}
		} else if gateway.DeviceType == device_enum.Device_DOCK3.Value() {
			// 机场3，降落以后的关盖
			if eventsReceiver.Output.Progress.CurrentStep.Value() == 28 {
				updateFlag = true
			}
		}

		if updateFlag {
			s.logger.Infof("drone landing. percent to 99.")
			s.db.Model(&model.InspectionJobRecord{}).Where("id = ?", jobRecord.ID).Update("percent", 99)
		}
	}

	var jobStatus int
	status := eventsReceiver.Output.Status

	if status.IsEnd() {
		// 批量删掉桥梁绕飞的航线锁
		if gatewaySn == "8UUXN2H00A011H" && (jobRecord.WaylineManageID == 214 || jobRecord.WaylineManageID == 268) {
			err = s.waylineRedisService.BatchDelWaylineJobTransferLock(gatewaySn)
			if err != nil {
				s.logger.Errorf("flightTaskProgressHandle, BatchDelWaylineJobTransferLock failed, jobId: %s, err: %v", jobId, err)
				return err
			}
			s.logger.Infof("flightTaskProgressHandle, BatchDelWaylineJobTransferLock success, gatewaySn: %s", gatewaySn)
		}

		if jobRecord.Percent < 100 {
			s.db.Model(&model.InspectionJobRecord{}).Where("id = ?", jobRecord.ID).Update("percent", 100)
		}

		if cloud_enum.FlightTaskStatus_OK.Value() == status.Value() {
			jobStatus = enum.Wayline_Success.Value()

			_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJob.JobID, gatewaySn, "航线任务执行完成", false)
		} else {
			jobStatus = enum.FAILED.Value()

			errorCodeEnum := cloud_enum.FindWaylineErrorCodeEnum(eventsReceiver.Result)
			_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJob.JobID, gatewaySn, fmt.Sprintf("航线任务执行异常: %s", errorCodeEnum.String()), true)
		}

		mediaCount := req.Data.Output.Ext.MediaCount
		s.logger.Infof("flightTaskProgressHandle, jobRecordId: %d, bid: %s, mediaCount: %d", jobRecord.ID, req.Bid, req.Data.Output.Ext.MediaCount)
		if mediaCount != 0 {
			// 如果没有media_count就初始化；如果有了就记录count
			var mediaFileCount *dto.MediaFileCountDTO
			mediaFileCount, err = s.mediaRedisService.getMediaCount(gatewaySn, jobId)
			if err != nil {
				return err
			}

			if mediaFileCount == nil {
				mediaFileCount = &dto.MediaFileCountDTO{
					JobId:        jobId,
					FileInfoList: make([]dto.FileInfo, 0),
					MediaCount:   &mediaCount,
				}
			} else {
				if len(mediaFileCount.FileInfoList) >= mediaCount {
					// 如果已上传完成
					// _ = NewJobLogsService().SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJob.JobID, gatewaySn, "任务产生的媒体资源全部上传完成", false)

					if jobStatus == enum.Wayline_Success.Value() {
						jobStatus = enum.Success.Value()
					}

					err = s.mediaRedisService.delMediaCount(gatewaySn, jobId)
					if err != nil {
						s.logger.Errorf("flight task progress: del media count failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
						return err
					}
				} else {
					// 没上传完
					mediaFileCount.MediaCount = &mediaCount

					if err = s.mediaRedisService.setMediaCount(gatewaySn, *mediaFileCount); err != nil {
						s.logger.Errorf("flight task progress set media count failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
						return err
					}
				}
			}

			err = s.db.Model(model.WaylineJob{}).Where("job_id = ? ", jobId).Update("media_count", mediaCount).Error
			if err != nil {
				return err
			}
		}

		if err = s.waylineRedisService.DelRunningWaylineJob(gatewaySn); err != nil {
			s.logger.Errorf("flight task progress del running wayline job failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
			return err
		}

		if err = s.waylineJobService.WaylineJobWorkDone(gatewaySn, jobId); err != nil {
			return err
		}
	} else {
		if cloud_enum.FlightTaskStatus_PAUSED.Value() == status.Value() {
			jobStatus = enum.PAUSED.Value()
		} else {
			jobStatus = enum.IN_PROGRESS.Value()
		}
	}

	if jobStatus != 0 {
		if err = s.waylineJobService.UpdateWaylineJobStatus(jobStatus, jobId, &eventsReceiver.Result); err != nil {
			return err
		}
	}

	waypointIndex := req.Data.Output.Ext.CurrentWaypointIndex

	inspectionStatusSvc := NewInspectionStatusService()
	inspectionStatus, err := inspectionStatusSvc.getInspectionStatus(gatewaySn)
	if err != nil {
		return err
	}

	if inspectionStatus != nil && inspectionStatus.Status.CurrentStatus == enum.InspectionStatusEnum_Wayline && inspectionStatus.Status.CurrentStep == enum.InspectionStatusStepEnum_Doing {
		// 当前航线是否为智能巡检正在执行的航线
		isCurrentWayline := false

		var behavior dto.InspectionBehavior

		if inspectionStatus.Behavior.BehaviorSequenceIndex != nil && *inspectionStatus.Behavior.BehaviorSequenceIndex < len(inspectionStatus.Behavior.BehaviorSequence) {
			behavior = inspectionStatus.Behavior.BehaviorSequence[*inspectionStatus.Behavior.BehaviorSequenceIndex]
			if behavior.Type == enum.InspectionBehavior_InGroundWaylineDeliver && behavior.BehaviorData.InGroundWaylineDeliverReq != nil && jobRecord.WaylineManageID == behavior.BehaviorData.InGroundWaylineDeliverReq.WaylineManageID {
				isCurrentWayline = true
			}
		}

		if isCurrentWayline {
			checkWaylineLock := fmt.Sprintf(fileds.CheckWaylineLock, gatewaySn)
			acquired, err := s.redis.SetNX(checkWaylineLock, "locked", fileds.CheckWaylineLockTimeout).Result()
			if err != nil {
				return err
			}

			if acquired {
				defer s.redis.Del(checkWaylineLock)

				if status.IsEnd() && cloud_enum.FlightTaskStatus_OK.Value() != status.Value() {
					err = inspectionStatusService.delInspectionStatus(gatewaySn)
					if err != nil {
						return err
					}

					err = inspectionStatusService.delExecuteDecisionLock(gatewaySn)
					if err != nil {
						return err
					}

					// todo v3 执行异常
				} else {
					// 执行正常
					isDoNext := false
					if behavior.BehaviorData.InGroundWaylineDeliverReq.TargetPoint == nil {
						// 目标航点是要求到达终点
						if status.IsEnd() {
							isDoNext = true
						}
					} else {
						// 目标航点要求到达指定航点
						if *behavior.BehaviorData.InGroundWaylineDeliverReq.TargetPoint == waypointIndex {
							s.logger.Infof("flightTaskProgressHandle, target waypoint reached, jobId: %s, dockSn: %s, waypointIndex: %d", jobId, gatewaySn, waypointIndex)
							isDoNext = true
						}
					}

					if isDoNext {
						// 流转状态
						inspectionStatus.Status.CurrentStep = enum.InspectionStatusStepEnum_After
						err = inspectionStatusSvc.setInspectionStatus(gatewaySn, inspectionStatus)
						if err != nil {
							return err
						}

						executeDecisionLockKey := fmt.Sprintf(fileds.ExecuteDecisionLock, gatewaySn)
						s.redis.Del(executeDecisionLockKey)
					}
				}
			}
		}
	}

	if !status.IsEnd() && gatewaySn == "8UUXN2H00A011H" && ((jobRecord.WaylineManageID == 214 && waypointIndex == 5) || (jobRecord.WaylineManageID == 268 && waypointIndex == 6)) {
		go func() {
			_, isControlCmdExist := fileds.ControlCmdMap[jobRecord.WaylineManageID]
			if isControlCmdExist {
				err = s.flightTaskTransfer(gatewaySn, gateway.ChildSN, jobId, fileds.ControlCmdMap[jobRecord.WaylineManageID])
				if err != nil {
					s.logger.Errorf("FlightTaskProgressHandle, flightTaskTransfer failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
					return
				}
			} else {
				s.logger.Infof("FlightTaskProgressHandle, no control cmd, waylineManageID: %d, dockSn: %s", jobRecord.WaylineManageID, gatewaySn)
			}
		}()
	}

	if req.NeedReply == 1 {
		err = cloudinfra.EventsReply(fileds.FLIGHTTASK_PROGRESS, gatewaySn, req.Bid, req.Tid)
		if err != nil {
			s.logger.Errorf("FlightTaskProgressHandle, FlightTaskProgressReply failed, jobId: %s, err: %v", jobId, err)
			return err
		}
	}

	return nil
}

// jobId 作为lock key。可能为 waylineJobId or inFlightWaylineJobId
// flightTaskTransfer 为了渡过信号丢失区域，自动执行编排指令，航线任务切换转移
func (s *FightTaskService) flightTaskTransfer(gatewaySn, droneSn, jobId string, controlCmd []*dto.AutoDroneControl) error {
	// 锁
	lockKey := fmt.Sprintf(fileds.WaylineJobTransferLockPrefix, gatewaySn, jobId)
	lockTimeout := 30 * time.Minute

	acquired, err := s.redis.SetNX(lockKey, "locked", lockTimeout).Result()
	if err != nil {
		return fmt.Errorf("failed to acquire lock: %v", err)
	}
	if !acquired {
		s.logger.Infof("flightTaskTransfer, jobId: %s, lock already exists", jobId)
		return nil
	}
	//defer s.redis.Del(lockKey)
	serviceKey := fileds.AutonomousFlyingAround
	// wsManager := repo.NewWsManager()
	// wsManager.BroadCastData([]byte("flightTaskTransfer"), serviceKey, gatewaySn, "")

	s.logger.Infof("flightTaskTransfer, do flightTaskTransfer, jobId: %s", jobId)

	err = NewMediaService().uploadFlighttaskMediaPrioritize(jobId, gatewaySn)
	if err != nil {
		s.logger.Errorf("flightTaskTransfer, uploadFlighttaskMediaPrioritize failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
	}

	// 接管
	err, errMsg := NewCockpitFlightService().DroneTakeOver(droneSn)
	if err != nil {
		s.logger.Errorf("flightTaskTransfer, drone take over failed, jobId: %s, err: %v, errMsg: %s", jobId, err, errMsg)
		return err
	}

	// 等待3s
	time.Sleep(3 * time.Second)

	// 执行编排指令
	wsManager := repo.NewWsManager()
	dockControlSvc := NewDockControlService()
	dockDRCSvc := NewDockDRCService()
	waylineManageSvc := NewWaylineManageService()
	waylineFileSvc := NewWaylineFileService()
	deviceSvc := NewDeviceService()
	liveStreamSvc := NewLiveStreamService()
	payloadSvc := NewPayloadService()
	//waylineRedisSvc := NewWaylineRedisService()

	connID := fmt.Sprintf("%s-%s", fileds.ServerRealTimeControl, gatewaySn)

	for _, cmd := range controlCmd {

		respData := map[string]interface{}{
			"model":  "自主飞行",
			"action": cmd.BusinessAction,
		}

		resp := make(map[string]map[string]interface{})
		resp[serviceKey] = respData

		encodedData, err := json.Marshal(resp)
		if err != nil {
			return err
		}

		wsManager.BroadCastData(encodedData, serviceKey, droneSn, "")
		switch cmd.Type {
		case enum.DroneControl:
			// 飞控
			if cmd.DroneControlReq == nil {
				return fmt.Errorf("flightTaskTransfer, drone control req is nil")
			}

			cmd.DroneControlReq.DockSn = gatewaySn

			err = dockDRCSvc.DeviceDrcCmd(*cmd.DroneControlReq)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, drone control failed, jobId: %s, err: %v", jobId, err)
				return err
			}
			s.logger.Infof("flightTaskTransfer, drone control success, jobId: %s, x:%f, y:%f, h:%f, w:%f", jobId, cmd.DroneControlReq.X, cmd.DroneControlReq.Y, cmd.DroneControlReq.H, cmd.DroneControlReq.W)

		case enum.PayloadCommand:
			// 云台控制
			if cmd.PayloadCommandsReq == nil {
				return fmt.Errorf("flightTaskTransfer, payload commands req is nil")
			}

			cmd.PayloadCommandsReq.DockSn = gatewaySn

			_, err, errMsg = dockControlSvc.PayloadCmd(*cmd.PayloadCommandsReq)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, payload cmd failed, jobId: %s, err: %v, errMsg: %s", jobId, err, errMsg)

				if cmd.PayloadCommandsReq.Method != fileds.CAMERA_PHOTO_TAKE {
					return err
				}
			} else {
				s.logger.Infof("flightTaskTransfer, payload cmd success, jobId: %s, method:%s", jobId, cmd.PayloadCommandsReq.Method)
			}

		case enum.InFlightWaylineDeliver:
			// 空中下发航线
			if cmd.InFlightWaylineDeliver == nil {
				return fmt.Errorf("flightTaskTransfer, in flight wayline deliver req is nil")
			}

			waylineManageID := cmd.InFlightWaylineDeliver.WaylineManageID
			waylineManage, err := waylineManageSvc.GetWaylineManageById(waylineManageID)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, GetWaylineManageById failed, waylineManageID: %d, err: %v", waylineManageID, err)
				return err
			}

			waylineFile, err := waylineFileSvc.GetWaylineById(waylineManage.WaylineFileID)
			if err != nil {
				return err
			}

			inFlightJobId, code, err := s.InFlightWaylineDeliver(gatewaySn, waylineFile.ObjectKey, cmd.InFlightWaylineDeliver.RthAltitude, waylineManageID)
			if err != nil {
				if code != nil {
					s.logger.Errorf("flightTaskTransfer, InFlightWaylineDeliver failed, jobId: %s, err: %v, code: %d", jobId, err, *code)
				} else {
					s.logger.Errorf("flightTaskTransfer, InFlightWaylineDeliver failed, jobId: %s, err: %v", jobId, err)
				}
				return err
			}

			s.logger.Infof("flightTaskTransfer, do InFlightWaylineDeliver success, inFlightWaylineId: %s", inFlightJobId)

		case enum.ReturnHome:
			var device *model.Device
			device, err = deviceSvc.GetDeviceBySn(gatewaySn)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, %s GetDeviceBySn failed, gatewaySn: %s, err: %v", cmd.Type.String(), gatewaySn, err)
				return err
			}

			if device == nil || device.JobId == "" {
				return fmt.Errorf("flightTaskTransfer, %s device is nil or jobId is empty", cmd.Type.String())
			}

			_, err, errMsg = s.ReturnHome(device.JobId)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, ReturnHome failed, jobId: %s, err: %v, errMsg: %s", jobId, err, errMsg)
				return err
			}

			s.logger.Infof("flightTaskTransfer, do ReturnHome success, gatewaySn: %s", gatewaySn)

		case enum.FlighttaskPause:
			// 暂停地面航线
			var device *model.Device
			device, err = deviceSvc.GetDeviceBySn(gatewaySn)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, %s GetDeviceBySn failed, gatewaySn: %s, err: %v", cmd.Type.String(), gatewaySn, err)
				return err
			}

			if device == nil || device.JobId == "" {
				return fmt.Errorf("flightTaskTransfer, %s device is nil or jobId is empty", cmd.Type.String())
			}

			_, err, errMsg = s.UpdateFlightTaskStatus(device.JobId, enum.WaylineTaskStatus_PAUSED)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, FlighttaskPause failed, jobId: %s, err: %v, errMsg: %s", device.JobId, err, errMsg)
				return err
			}

			s.logger.Infof("flightTaskTransfer, pause flight task success, jobId: %s", device.JobId)

		case enum.FlighttaskUndo:
			// 取消地面航线
			var device *model.Device
			device, err = deviceSvc.GetDeviceBySn(gatewaySn)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, %s GetDeviceBySn failed, gatewaySn: %s, err: %v", cmd.Type.String(), gatewaySn, err)
				return err
			}

			if device == nil || device.JobId == "" {
				return fmt.Errorf("flightTaskTransfer, %s device is nil or jobId is empty", cmd.Type.String())
			}

			err = s.CancelFlightTask(gatewaySn, device.JobId)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, cancel flight task failed, jobId: %s, err: %v", device.JobId, err)
				return err
			}

			s.logger.Infof("FlightTaskProgressHandle, jobId: %s, cancel flight task success", device.JobId)

		case enum.LiveLensChange:
			// 变焦
			if cmd.LiveLensChange == nil {
				return fmt.Errorf("flightTaskTransfer, live lens change req is nil")
			}

			err, errMsg = liveStreamSvc.LiveLensChange(gatewaySn, cmd.LiveLensChange.PayloadIndex, cmd.LiveLensChange.CameraType)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, LiveLensChange failed, jobId: %s, err: %v, errMsg: %s", jobId, err, errMsg)
				return err
			}

			s.logger.Infof("flightTaskTransfer, do LiveLensChange success, jobId: %s, cameraType: %s", jobId, cmd.LiveLensChange.CameraType)

		case enum.CameraScreenDrag:
			// 云台调整
			if cmd.CameraScreenDrag == nil {
				return fmt.Errorf("flightTaskTransfer, camera screen drag is nil")
			}

			for i := 0; i < cmd.CameraScreenDrag.DoNumber; i++ {
				payloadCmd := dto.PayloadCommandsReq{
					DockSn: gatewaySn,
					Method: fileds.Camera_Screen_Drag,
					Data: device_request.DronePayloadRequest{
						PayloadIndex: cmd.CameraScreenDrag.PayloadIndex,
						Locked:       cmd.CameraScreenDrag.Locked,
						PitchSpeed:   cmd.CameraScreenDrag.PitchSpeed,
						YawSpeed:     cmd.CameraScreenDrag.YawSpeed,
					},
				}

				_, err, errMsg = dockControlSvc.PayloadCmd(payloadCmd)
				if err != nil {
					s.logger.Errorf("flightTaskTransfer, camera_screen_drag failed, do_time: %d, pitch_speed: %v, yaw_speed: %v, jobId: %s, err: %v, errMsg: %s", i, cmd.CameraScreenDrag.PitchSpeed, cmd.CameraScreenDrag.YawSpeed, jobId, err, errMsg)
					continue
				} else {
					s.logger.Infof("flightTaskTransfer, camera_screen_drag success, do_time: %d, pitch_speed: %v, yaw_speed: %v, jobId: %s", i, cmd.CameraScreenDrag.PitchSpeed, cmd.CameraScreenDrag.YawSpeed, jobId)
				}

				if cmd.CameraScreenDrag.WaitTime != nil {
					time.Sleep(*cmd.CameraScreenDrag.WaitTime)
				}
			}

		case enum.LightModeSet:
			// 设置探照灯
			if cmd.PayloadLightModeSetReq == nil || cmd.PayloadLightModeSetReq.Mode == nil {
				return fmt.Errorf("flightTaskTransfer, PayloadLightModeSetReq req is nil")
			}

			cmd.PayloadLightModeSetReq.DockSn = gatewaySn

			err, errMsg = payloadSvc.LightModeSet(*cmd.PayloadLightModeSetReq)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer, LightModeSet failed, jobId: %s, err: %v, errMsg: %s", jobId, err, errMsg)
				continue
			}

			s.logger.Infof("flightTaskTransfer, do LightModeSet success, jobId: %s, type: %d", jobId, *cmd.PayloadLightModeSetReq.Mode)

		case enum.AlgorithmControl:
			// 算法控制 - 请求算法做控制决策并等待执行完成
			s.logger.Infof("flightTaskTransfer: requesting algorithm control, jobId: %s", jobId)

			if cmd.AutoDroneControlReq == nil {
				return fmt.Errorf("flightTaskTransfer, AutoDroneControlReq req is nil")
			}

			// 检查WebSocket连接
			if _, ok := wsManager.WsConn[connID]; !ok {
				s.logger.Errorf("flightTaskTransfer: websocket connection not found, connID: %s", connID)
				return fmt.Errorf("websocket connection not found: %s", connID)
			}

			// 1.设置AI控制锁，表示算法正在进行控制决策
			aiControlLockKey := fmt.Sprintf(fileds.AI_Control_Lock, gatewaySn)
			//aiControlLockTimeout := 5 * time.Minute
			aiControlLockTimeout := 20 * time.Minute

			// 上锁
			var aiAcquired bool
			aiAcquired, err = s.redis.SetNX(aiControlLockKey, jobId, aiControlLockTimeout).Result()
			if err != nil {
				s.logger.Errorf("flightTaskTransfer: failed to set ai control lock, jobId: %s, err: %v", jobId, err)
				return err
			}
			if !aiAcquired {
				s.logger.Errorf("flightTaskTransfer: ai control lock already exists, skip algorithm control, jobId: %s", jobId)
				return fmt.Errorf("ai control lock already exists, skip algorithm control, jobId: %s", jobId)
			}

			// todo 2.准备实时控制请求数据
			// 1.获取深度信息
			// 2.缓存

			//var waylineJob *dto.InFlightWaylineJob
			//waylineJob, err = waylineRedisSvc.GetRunningInFlightWaylineJob(gatewaySn)
			//if err != nil {
			//	s.logger.Errorf("flightTaskTransfer, get running wayline job failed, gatewaySn: %s, err: %v", gatewaySn, err)
			//	return err
			//}
			//
			//if waylineJob == nil || waylineJob.WaylineManageId == 0 {
			//	s.logger.Errorf("flightTaskTransfer, wayline job is nil, dockSn: %s", gatewaySn)
			//	return err
			//}
			//
			//realTimeControlReq, ok := fileds.RealTimeControlReqData[waylineJob.WaylineManageId]
			//if !ok {
			//	s.logger.Errorf("flightTaskTransfer: real time control req not found, WaylineManageId: %d", waylineJob.WaylineManageId)
			//	return err
			//}

			// 3.通过WebSocket向算法服务发送控制请求
			req := dto.JobDataProcessingReq{
				Type: fileds.RealTimeControlReq,
				Data: cmd.AutoDroneControlReq.RealTimeControlReq,
			}

			var bytes []byte
			bytes, err = json.Marshal(&req)
			if err != nil {
				s.logger.Errorf("flightTaskTransfer: failed to marshal control request, jobId: %s, err: %v", jobId, err)
				_ = s.redis.Del(aiControlLockKey)
				return err
			}

			// 发送算法控制请求
			wsManager.SendData(bytes, connID)
			s.logger.Infof("flightTaskTransfer: sent algorithm control request, jobId: %s", jobId)

			// 自旋等待算法控制完成（等待锁被释放）
			// 每2秒检查一次锁是否还存在，如果锁被释放则表示算法控制完成
			//maxWaitTime := 4 * time.Minute // 最大等待时间
			maxWaitTime := 20 * time.Minute // 最大等待时间
			startTime := time.Now()

			for {
				// 检查是否超时
				if time.Since(startTime) > maxWaitTime {
					s.logger.Errorf("flightTaskTransfer: algorithm control timeout, jobId: %s", jobId)
					_ = s.redis.Del(aiControlLockKey)
					return fmt.Errorf("algorithm control timeout")
				}

				// 检查锁是否还存在
				var exists int64
				exists, err = s.redis.Exists(aiControlLockKey).Result()
				if err != nil {
					s.logger.Errorf("flightTaskTransfer: failed to check ai control lock, jobId: %s, err: %v", jobId, err)
					time.Sleep(2 * time.Second)
					continue
				}

				// 如果锁不存在，表示算法控制已完成
				if exists == 0 {
					s.logger.Infof("flightTaskTransfer: algorithm control completed, jobId: %s", jobId)
					break
				}

				// 等待2秒后再次检查
				time.Sleep(2 * time.Second)
			}
		}

		if cmd.WaitTime != nil {
			time.Sleep(*cmd.WaitTime)
		}
	}

	return nil
}

// returnHomeInfoHandle 返航信息上报处理
func (s *FightTaskService) returnHomeInfoHandle(req mqtt.TopicEventsRequest[request.ReturnHomeInfo]) error {
	gatewaySn := req.Gateway
	//jobId := req.Data.FlightId

	//var status int
	//flag := false
	//
	//waylineJob, err := s.waylineJobService.GetWaylineJobByJobId(jobId)
	//if err != nil {
	//	s.logger.Errorf("ReturnHomeInfoHandle, GetWaylineJobByJobId failed, jobId: %s, err: %v", jobId, err)
	//	return err
	//}
	//
	//switch waylineJob.Status {
	//case enum.WorkDoneAndNotReturned.Value():
	//	status = enum.WorkDoneAndReturning.Value()
	//	flag = true
	//case enum.WorkDoneAndReturnedErr.Value():
	//	status = enum.WorkDoneAndReturning.Value()
	//	flag = true
	//case enum.WorkInterruptAndNotReturned.Value():
	//	status = enum.WorkInterruptAndReturning.Value()
	//	flag = true
	//case enum.WorkInterruptAndReturnedErr.Value():
	//	status = enum.WorkInterruptAndReturning.Value()
	//	flag = true
	//}

	//_ = s.waylineJobService.SaveJobLogByDrone(waylineJob.JobID, gatewaySn, "自动巡检任务：无人机返航信息上报")

	//if flag {
	//	if err = s.waylineJobService.UpdateWaylineJobStatus(status, jobId, nil); err != nil {
	//		s.logger.Errorf("ReturnHomeInfoHandle, update wayline job status failed, jobId: %s, err: %v", jobId, err)
	//		return err
	//	}
	//}

	// 将返航信息记录redis，用于确定无人机是否达到最终点位
	//if err = s.waylineRedisService.SetReturnHomeInfo(gatewaySn, req.Data); err != nil {
	//	s.logger.Errorf("ReturnHomeInfoHandle, SetReturnHomeInfo failed, jobId: %s, err: %v", jobId, err)
	//	return err
	//}

	err := cloudinfra.EventsReply(fileds.RETURN_HOME_INFO, gatewaySn, req.Bid, req.Tid)
	if err != nil {
		s.logger.Errorf("ReturnHomeInfoHandle, ReturnHomeInfoReply failed, err: %v", err)
		return err
	}

	return nil
}

// exitHomingNotifyHandle 设备返航退出状态通知上报处理
func (s *FightTaskService) exitHomingNotifyHandle(req mqtt.TopicEventsRequest[request.ExitHomingNotify]) error {
	gatewaySn := req.Gateway
	jobId := req.Bid

	waylineJob, err := s.waylineJobService.GetWaylineJobByJobId(jobId)
	if err != nil {
		s.logger.Errorf("ExitHomingNotifyHandle, GetWaylineJobByJobId failed, jobId: %s, err: %v", jobId, err)
		return err
	}

	//var status int
	var log string

	if req.Data.Action.Value() == 0 {
		log = fmt.Sprintf("无人机退出”返航退出状态“，原因：%s", req.Data.Reason.String())

		//switch waylineJob.Status {
		//case enum.WorkInterruptAndReturnedErr.Value():
		//	status = enum.WorkInterruptAndReturning.Value()
		//case enum.WorkDoneAndReturnedErr.Value():
		//	status = enum.WorkDoneAndReturning.Value()
		//}

	} else if req.Data.Action.Value() == 1 {
		log = fmt.Sprintf("无人机进入”返航退出状态“，原因：%s", req.Data.Reason)

		//switch waylineJob.Status {
		//case enum.WorkInterruptAndReturning.Value():
		//	status = enum.WorkInterruptAndReturnedErr.Value()
		//case enum.WorkDoneAndReturning.Value():
		//	status = enum.WorkDoneAndReturnedErr.Value()
		//}
	}

	//if err = s.waylineJobService.UpdateWaylineJobStatus(status, jobId, nil); err != nil {
	//	s.logger.Errorf("ExitHomingNotifyHandle, update wayline job status failed, jobId: %s, err: %v", jobId, err)
	//	return err
	//}

	_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJob.JobID, gatewaySn, log, false)

	err = cloudinfra.EventsReply(fileds.EXIT_HOMING_NOTIFY, gatewaySn, req.Bid, req.Tid)
	if err != nil {
		s.logger.Errorf("ExitHomingNotifyHandle, ExitHomingNotifyReply failed, jobId: %s, err: %v", jobId, err)
		return err
	}

	return nil
}

// ReturnHome 一键返航
func (s *FightTaskService) ReturnHome(jobId string) (*mqtt.CommonTopicResponse[mqtt.MqttReply], error, string) {
	waylineJob, err := s.waylineJobService.GetWaylineJobByJobId(jobId)
	if err != nil {
		s.logger.Errorf("ReturnHome, GetWaylineJobByJobId failed, jobId: %s, err: %v", jobId, err)
		return nil, err, "一键返航失败"
	}

	dockSN := waylineJob.DockSN

	flag := false

	switch waylineJob.Status {
	case enum.IN_PROGRESS.Value():
		flag = true
	case enum.FAILED.Value():
		flag = true
	case enum.PAUSED.Value():
		flag = true
	}

	if !flag {
		statusEnum := enum.FindWaylineJobStatusEnum(waylineJob.Status)
		return nil, fmt.Errorf("当前任务状态不支持一键返航, waylineJobId: %s, 状态为: %s", jobId, statusEnum.String()), "一键返航失败：当前任务状态不支持一键返航"
	}

	serviceReply, err := cloudinfra.ReturnHome(dockSN)
	if err != nil {
		s.logger.Errorf("return home failed, jobId: %s, err: %v", jobId, err)
		return nil, err, "一键返航失败"
	}

	//{"bid": "", "data": {"result": 0}, "method": "return_home", "tid": "5d8d433f-bf04-49d5-a986-4e631fc976a7", "timestamp": 1726052745672}
	if serviceReply.Data.Result != nil && *serviceReply.Data.Result != 0 {
		err = fmt.Errorf("一键返航失败, jobId: %s, result: %d", jobId, serviceReply.Data.Result)

		codeEnum := wayline_cloud_enum.FindWaylineErrorCodeEnum(*serviceReply.Data.Result)
		if codeEnum == wayline_cloud_enum.WaylineErrorCodeEnumUNKNOWN {
			return nil, err, "一键返航失败"
		} else {
			return nil, err, fmt.Sprintf("一键返航失败：%s", codeEnum.String())
		}
	}

	_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, waylineJob.JobID, waylineJob.DockSN, "无人机一键返航", false)

	return serviceReply, nil, ""
}

// UpdateFlightTaskStatus 修改航线任务状态 暂停/恢复
func (s *FightTaskService) UpdateFlightTaskStatus(jobId string, statusEnum enum.WaylineTaskStatusEnum) (errCode *int, err error, errMsg string) {
	waylineJob, err := s.waylineJobService.GetWaylineJobByJobId(jobId)
	if err != nil {
		return nil, err, fmt.Sprintf("航线%s失败", statusEnum.String())
	}

	jobStatusEnum := enum.FindWaylineJobStatusEnum(waylineJob.Status)
	if jobStatusEnum.IsEnd() || enum.Waiting == jobStatusEnum || enum.Prepared == jobStatusEnum {
		err = fmt.Errorf("the wayline job status does not match, and the operation cannot be performed. jobId: %s, status: %s", jobId, jobStatusEnum.String())
		desc := fmt.Sprintf("当前任务状态不支持%s", statusEnum.String())
		return nil, err, fmt.Sprintf("航线%s失败：%s", statusEnum.String(), desc)
	}

	switch statusEnum {
	case enum.WaylineTaskStatus_PAUSED:
		errCode, err = s.pauseJob(waylineJob.DockSN, jobId, jobStatusEnum)
		break
	case enum.WaylineTaskStatus_RESUME:
		errCode, err = s.resumeJob(waylineJob.DockSN, jobId, jobStatusEnum)
		break
	}

	if err != nil {
		codeEnum := wayline_cloud_enum.WaylineErrorCodeEnumUNKNOWN
		if errCode != nil {
			codeEnum = wayline_cloud_enum.FindWaylineErrorCodeEnum(*errCode)
		}

		if codeEnum == wayline_cloud_enum.WaylineErrorCodeEnumUNKNOWN {
			return errCode, err, fmt.Sprintf("航线%s失败", statusEnum.String())
		} else {
			return errCode, err, fmt.Sprintf("航线%s失败：%s", statusEnum.String(), codeEnum.String())
		}
	}

	if errCode != nil {
		return errCode, nil, ""
	}

	return nil, nil, ""
}

// pauseJob 航线暂停
func (s *FightTaskService) pauseJob(dockSn string, jobId string, statusEnum enum.WaylineJobStatusEnum) (errCode *int, err error) {
	pausedWaylineJobId, _ := s.waylineRedisService.GetPausedWaylineJob(dockSn)

	if enum.PAUSED == statusEnum && jobId == pausedWaylineJobId {
		if err = s.waylineRedisService.SetPausedWaylineJob(dockSn, jobId); err != nil {
			return nil, err
		}
		_ = s.waylineRedisService.DelRunningWaylineJob(dockSn)
		return nil, err
	}

	serviceReply, err := cloudinfra.FlightTaskPause(dockSn)
	if err != nil {
		s.logger.Errorf("Failed to pause wayline job. jobId: %s, err: %v", jobId, err)
		return nil, err
	}

	if *serviceReply.Data.Result == 319046 {
		return serviceReply.Data.Result, nil
	}

	if *serviceReply.Data.Result != 0 {
		return serviceReply.Data.Result, fmt.Errorf("failed to pause wayline job. jobId: %s, result: %d", jobId, *serviceReply.Data.Result)
	}

	_ = s.waylineRedisService.DelRunningWaylineJob(dockSn)
	_ = s.waylineRedisService.SetPausedWaylineJob(dockSn, jobId)

	_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, jobId, dockSn, "航线任务暂停", false)

	return nil, nil
}

// resumeJob 航线恢复
func (s *FightTaskService) resumeJob(dockSn string, jobId string, statusEnum enum.WaylineJobStatusEnum) (errCode *int, err error) {
	runningWaylineJob, _ := s.waylineRedisService.GetRunningWaylineJob(dockSn)

	if enum.IN_PROGRESS == statusEnum && runningWaylineJob != nil && jobId == runningWaylineJob.Bid && cloud_enum.FlightTaskStatus_IN_PROGRESS == *runningWaylineJob.Output.Status {
		if err = s.waylineRedisService.SetRunningWaylineJob(dockSn, *runningWaylineJob); err != nil {
			return nil, err
		}
		_ = s.waylineRedisService.DelPausedWaylineJob(dockSn)
		return nil, err
	}

	serviceReply, err := cloudinfra.FlightTaskRecovery(dockSn)
	if err != nil {
		s.logger.Errorf("Failed to resume wayline job. jobId: %s, err: %v", jobId, err)
		return nil, err
	}

	if *serviceReply.Data.Result != 0 {
		return serviceReply.Data.Result, fmt.Errorf("failed to resume wayline job. jobId: %s, result: %d", jobId, *serviceReply.Data.Result)
	}

	if runningWaylineJob != nil {
		_ = s.waylineRedisService.SetRunningWaylineJob(dockSn, *runningWaylineJob)
	}

	_ = s.waylineRedisService.DelPausedWaylineJob(dockSn)

	_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, jobId, dockSn, "航线任务恢复", false)

	return nil, nil
}

// initFlightTaskTrajectory 将机场坐标作为无人机轨迹初始
func (s *FightTaskService) initFlightTaskTrajectory(dockSn, droneSn string, jobRecordId int) error {
	drone, err := NewDroneService().GetDroneByVehicleID(droneSn)
	if err != nil {
		s.logger.Errorf("get drone by vehicle id error: %v", err)
		return err
	}

	var dockOsd *device_request.OsdDock

	if dockIsOnline, _ := s.deviceRedisService.CheckDeviceOnline(dockSn); dockIsOnline {
		if dockOsd, err = s.deviceRedisService.getDockOsd(dockSn); err != nil {
			s.logger.Errorf("get dock osd error: %v", err)
			return err
		}
	}

	if dockOsd != nil {
		standardGPS := GPS{
			Longitude: dockOsd.Longitude,
			Latitude:  dockOsd.Latitude,
			Height:    dockOsd.Height,
		}
		bdGPS := GPS{
			Height: dockOsd.Height,
		}

		if bdGPS.Longitude, bdGPS.Latitude, err = utils.CoordinateTransformV2(standardGPS.Longitude, standardGPS.Latitude, fileds.StandardToBd09ll); err != nil {
			bdGPS.Longitude = dockOsd.Longitude
			bdGPS.Latitude = dockOsd.Latitude
			s.logger.Warnf("经纬度坐标转换失败: %s", err.Error())
		}

		err = NewDroneMsgService().handleTaskTrajectory(drone, standardGPS, bdGPS)
		if err != nil {
			s.logger.Errorf("save drone first trajectory error: %v", err)
			return err
		}

		taskDetailGPSKey := fmt.Sprintf(fileds.TaskDetailGpsPrefix, droneSn)
		gpsResult, _ := s.redis.Get(taskDetailGPSKey).Result()
		if gpsResult != "" {
			if err = s.inspectionJobRecordService.SaveTaskTrajectoryById(jobRecordId, gpsResult); err != nil {
				s.logger.Errorf("save task trajectory error: %v", err)
				return err
			}
		}
	}

	return nil
}

// CancelFlightTask 取消任务。
func (s *FightTaskService) CancelFlightTask(dockSn, jobId string) error {
	serviceReply, err := cloudinfra.FlightTaskUndo(dockSn, request.FlightTaskUndoRequest{
		FlightIds: []string{jobId},
	})
	if err != nil {
		s.logger.Errorf("Failed to cancel wayline job. jobId: %s, err: %v", jobId, err)
		return err
	}

	if *serviceReply.Data.Result != 0 {
		return fmt.Errorf("failed to cancel wayline job. jobId: %s, result: %d", jobId, *serviceReply.Data.Result)
	}

	return nil
}

// InFlightWaylineDeliver 空中下发航线
func (s *FightTaskService) InFlightWaylineDeliver(dockSn, objectKey string, rthAltitude, waylineManageId int) (string, *int, error) {
	url, err := NewOssService().GetObjectSignURL(objectKey, waylineFileSignExpire)
	if err != nil {
		return "", nil, err
	}

	fingerprint, err := utils.GetRemoteFileMD5(url)
	if err != nil {
		return "", nil, err
	}

	jobId := uuid.New().String()

	req := request.InFlightWaylineDeliver{
		File: request.FlightTaskFile{
			Url:         url,
			Fingerprint: fingerprint,
		},
		InFlightWaylineId:     jobId,
		OutOfControlAction:    cloud_enum.FindOutOfControlActionEnum(cloud_enum.HOVERING.Value()),
		ExitWaylineWhenRcLost: cloud_enum.FindExitWaylineWhenRcLostEnum(cloud_enum.EXECUTE_RC_LOST_ACTION.Value()),
		RthAltitude:           rthAltitude,
		RthMode:               cloud_enum.FindRthModeEnum(cloud_enum.PRESET_HEIGHT.Value()),
		WaylinePrecisionType:  cloud_enum.FindWaylinePrecisionTypeEnum(cloud_enum.RTK.Value()),
	}

	serviceReply, err := cloudinfra.InFlightWaylineDeliver(dockSn, req)
	if err != nil {
		s.logger.Errorf("Failed to InFlightWaylineDeliver. jobId: %s, err: %v", req.InFlightWaylineId, err)
		return "", nil, err
	}

	if *serviceReply.Data.Result != 0 {
		return "", serviceReply.Data.Result, fmt.Errorf("failed to InFlightWaylineDeliver, jobId: %s, code: %d", req.InFlightWaylineId, *serviceReply.Data.Result)
	}

	data := dto.InFlightWaylineJob{
		InFlightWaylineProgress: nil,
		WaylineManageId:         waylineManageId,
	}

	err = s.waylineRedisService.SetRunningInFlightWaylineJob(dockSn, data)
	if err != nil {
		s.logger.Errorf("InFlightWaylineDeliver, set running wayline job failed, jobId: %s, err: %v", jobId, err)
		return "", nil, err
	}

	return jobId, nil, nil
}

func (s *FightTaskService) inFlightWaylineProgressHandle(req mqtt.TopicEventsRequest[request.InFlightWaylineProgress]) error {
	gatewaySn := req.Gateway
	jobId := req.Data.InFlightWaylineId

	oldWaylineJob, err := s.waylineRedisService.GetRunningInFlightWaylineJob(gatewaySn)
	if err != nil {
		s.logger.Errorf("inFlightWaylineProgressHandle, get running wayline job failed, jobId: %s, err: %v", jobId, err)
		return err
	}

	if oldWaylineJob == nil {
		s.logger.Errorf("inFlightWaylineProgressHandle, get running wayline job nil, jobId: %s, err: %v", jobId, err)
		return fmt.Errorf("inFlightWaylineProgressHandle, get running wayline job nil, jobId: %s, err: %v", jobId, err)
	}

	waylineManageId := oldWaylineJob.WaylineManageId

	isEnd := false
	isFailed := false

	if req.Data.Status == 5 {
		// 任务取消
		isEnd = true
		isFailed = true
	} else if req.Data.Status == 6 {
		// 任务成功
		isEnd = true
	} else if req.Data.Status == 7 {
		// 任务失败
		isEnd = true
		isFailed = true
	}

	if !isEnd {
		data := dto.InFlightWaylineJob{
			InFlightWaylineProgress: &req.Data,
			WaylineManageId:         waylineManageId,
		}

		err = s.waylineRedisService.SetRunningInFlightWaylineJob(gatewaySn, data)
		if err != nil {
			s.logger.Errorf("inFlightWaylineProgressHandle, set running wayline job failed, jobId: %s, err: %v", jobId, err)
			return err
		}
	} else {
		err = s.waylineRedisService.DelRunningInFlightWaylineJob(gatewaySn)
		if err != nil {
			s.logger.Errorf("inFlightWaylineProgressHandle, del running wayline job failed, jobId: %s, err: %v", jobId, err)
			return err
		}
	}

	// 任务成功完成
	if isEnd && !isFailed {
		s.logger.Infof("inFlightWaylineProgressHandle: in flight wayline job: %s done. waylineManageId: %d", jobId, waylineManageId)

		var gateway *dto.DeviceDTO
		gateway, err = s.deviceRedisService.getDeviceOnline(gatewaySn)
		if err != nil {
			return err
		}

		_, isControlCmdExist := fileds.ControlCmdMap[waylineManageId]
		if isControlCmdExist {
			err = s.flightTaskTransfer(gatewaySn, gateway.ChildSN, jobId, fileds.ControlCmdMap[waylineManageId])
			if err != nil {
				s.logger.Errorf("inFlightWaylineProgressHandle, flightTaskTransfer failed, jobId: %s, dockSn: %s, err: %v", jobId, gatewaySn, err)
				return err
			}
		} else {
			s.logger.Infof("inFlightWaylineProgressHandle, no control cmd, jobId: %s, dockSn: %s", jobId, gatewaySn)
		}
	}

	waypointIndex := req.Data.WayPointIndex

	inspectionStatusSvc := NewInspectionStatusService()
	inspectionStatus, err := inspectionStatusSvc.getInspectionStatus(gatewaySn)
	if err != nil {
		return err
	}

	// 处理巡检状态逻辑
	if inspectionStatus != nil && inspectionStatus.Status.CurrentStep == enum.InspectionStatusStepEnum_Doing {
		err = s.handleInspectionStatusLogic(inspectionStatus, inspectionStatusSvc, gatewaySn, waylineManageId, waypointIndex, isEnd, isFailed)
		if err != nil {
			return err
		}
	}

	return nil
}

// handleInspectionStatusLogic 处理巡检状态逻辑的公共方法
func (s *FightTaskService) handleInspectionStatusLogic(inspectionStatus *dto.InspectionStatus, inspectionStatusSvc *InspectionStatusService, gatewaySn string, waylineManageId, waypointIndex int, isEnd, isFailed bool) error {
	// 检查当前航线是否为正在执行的航线
	isCurrentWayline, behavior := s.checkCurrentWayline(inspectionStatus, waylineManageId)
	if !isCurrentWayline {
		return nil
	}

	// 获取分布式锁
	checkWaylineLock := fmt.Sprintf(fileds.CheckWaylineLock, gatewaySn)
	acquired, err := s.redis.SetNX(checkWaylineLock, "locked", fileds.CheckWaylineLockTimeout).Result()
	if err != nil {
		return err
	}

	if !acquired {
		return nil
	}
	defer s.redis.Del(checkWaylineLock)

	// 判断是否需要流转到下一步
	isDoNext := s.shouldProceedToNext(inspectionStatus.Status.CurrentStatus, behavior, waypointIndex, isEnd, isFailed)
	if !isDoNext {
		return nil
	}

	// 流转状态
	inspectionStatus.Status.CurrentStep = enum.InspectionStatusStepEnum_After
	err = inspectionStatusSvc.setInspectionStatus(gatewaySn, inspectionStatus)
	if err != nil {
		return err
	}

	// 释放执行决策锁
	executeDecisionLockKey := fmt.Sprintf(fileds.ExecuteDecisionLock, gatewaySn)
	s.redis.Del(executeDecisionLockKey)

	return nil
}

// checkCurrentWayline 检查当前航线是否为正在执行的航线
func (s *FightTaskService) checkCurrentWayline(inspectionStatus *dto.InspectionStatus, waylineManageId int) (bool, dto.InspectionBehavior) {
	var behavior dto.InspectionBehavior

	if inspectionStatus.Behavior.BehaviorSequenceIndex == nil ||
		*inspectionStatus.Behavior.BehaviorSequenceIndex >= len(inspectionStatus.Behavior.BehaviorSequence) {
		return false, behavior
	}

	behavior = inspectionStatus.Behavior.BehaviorSequence[*inspectionStatus.Behavior.BehaviorSequenceIndex]

	switch inspectionStatus.Status.CurrentStatus {
	case enum.InspectionStatusEnum_ReturnHome:
		return behavior.Type == enum.InspectionBehavior_ReturnHome &&
			behavior.BehaviorData.InspectionBehaviorReturnHome != nil &&
			waylineManageId == behavior.BehaviorData.InspectionBehaviorReturnHome.WaylineManageID, behavior
	case enum.InspectionStatusEnum_Wayline:
		return behavior.Type == enum.InspectionBehavior_InFlightWaylineDeliver &&
			behavior.BehaviorData.InFlightWaylineDeliverReq != nil &&
			waylineManageId == behavior.BehaviorData.InFlightWaylineDeliverReq.WaylineManageID, behavior
	default:
		return false, behavior
	}
}

// shouldProceedToNext 判断是否应该流转到下一步
func (s *FightTaskService) shouldProceedToNext(currentStatus enum.InspectionStatusEnum, behavior dto.InspectionBehavior, waypointIndex int, isEnd, isFailed bool) bool {
	if isEnd && isFailed {
		// TODO: v3 执行异常处理
		return false
	}

	switch currentStatus {
	case enum.InspectionStatusEnum_ReturnHome:
		// 返航状态：正常完成即可流转
		return isEnd && !isFailed
	case enum.InspectionStatusEnum_Wayline:
		// 航线状态：需要检查目标航点
		if isFailed {
			// 任务失败时不流转
			return false
		}

		if behavior.BehaviorData.InFlightWaylineDeliverReq.TargetPoint == nil {
			// 目标航点是要求到达终点
			return isEnd && !isFailed
		} else {
			// 目标航点要求到达指定航点
			return *behavior.BehaviorData.InFlightWaylineDeliverReq.TargetPoint == waypointIndex
		}
	default:
		return false
	}
}

// InFlightWaylineStop 暂停空中航线
func (s *FightTaskService) InFlightWaylineStop(dockSn, jobId string) error {
	serviceReply, err := cloudinfra.InFlightWaylineStop(dockSn, request.InFlightWaylineStopRequest{
		InFlightWaylineId: jobId,
	})
	if err != nil {
		s.logger.Errorf("Failed to InFlightWaylineStop. jobId: %s, err: %v", jobId, err)
		return err
	}

	if *serviceReply.Data.Result != 0 {
		return fmt.Errorf("failed to InFlightWaylineStop. jobId: %s, result: %d", jobId, *serviceReply.Data.Result)
	}

	return nil
}

// InFlightWaylineCancel 取消空中航线
func (s *FightTaskService) InFlightWaylineCancel(dockSn, jobId string) error {
	serviceReply, err := cloudinfra.InFlightWaylineCancel(dockSn)
	if err != nil {
		s.logger.Errorf("Failed to InFlightWaylineCancel. jobId: %s, err: %v", jobId, err)
		return err
	}

	if *serviceReply.Data.Result != 0 {
		return fmt.Errorf("failed to InFlightWaylineCancel. jobId: %s, result: %d", jobId, *serviceReply.Data.Result)
	}

	return nil
}

// flightTaskReady 条件任务类型-任务准备就绪通知处理
func flightTaskReadyHandle() {
}

// checkScheduledJob 处理定时型任务
func checkScheduledJob() {
}

// prepareConditionJob 处理条件型任务
func prepareConditionJob() {
}

func AutonomousFlyingAround(droneSn, jobId string, data []byte) {
	serviceKey := fileds.AutonomousFlyingAround
	wsManager := repo.NewWsManager()
	wsManager.BroadCastData(data, serviceKey, droneSn, "")
}
